//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_LEX_LEXERTL_MAR_17_2007_1008PM)
#define BOOST_SPIRIT_LEX_LEXERTL_MAR_17_2007_1008PM

#if defined(_MSC_VER)
#pragma once
#endif

//  These includes make available everything needed to use lexertl either 
//  standalone or as a lexer component for spirit::qi
#include <boost/spirit/home/<USER>
#include <boost/spirit/home/<USER>/lexer/lexertl/lexer.hpp>

#endif
