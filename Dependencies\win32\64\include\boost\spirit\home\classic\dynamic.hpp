/*=============================================================================
    Copyright (c) 2002-2003 <PERSON>
    Copyright (c) 2002 <PERSON>-Baeza
    Copyright (c) 2002-2003 <PERSON>
    http://spirit.sourceforge.net/

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#ifndef BOOST_SPIRIT_DYNAMIC_HPP
#define BOOST_SPIRIT_DYNAMIC_HPP

#include <boost/spirit/home/<USER>/version.hpp>

///////////////////////////////////////////////////////////////////////////////
//
//  Master header for Spirit.Dynamic
//
///////////////////////////////////////////////////////////////////////////////
#include <boost/spirit/home/<USER>/dynamic/if.hpp>
#include <boost/spirit/home/<USER>/dynamic/for.hpp>
#include <boost/spirit/home/<USER>/dynamic/while.hpp>
#include <boost/spirit/home/<USER>/dynamic/lazy.hpp>
#include <boost/spirit/home/<USER>/dynamic/stored_rule.hpp>
#include <boost/spirit/home/<USER>/dynamic/rule_alias.hpp>
#include <boost/spirit/home/<USER>/dynamic/select.hpp>
#include <boost/spirit/home/<USER>/dynamic/switch.hpp>

////////////////////////////////////////////////////////////////////////////////
#endif // BOOST_SPIRIT_DYNAMIC_HPP
