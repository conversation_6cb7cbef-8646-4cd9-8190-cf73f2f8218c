/*=============================================================================
    Copyright (c) 2001-2011 <PERSON>

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_OPERATOR_FEBRUARY_02_2007_0558PM)
#define BOOST_SPIRIT_OPERATOR_FEBRUARY_02_2007_0558PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/operator/sequence.hpp>
#include <boost/spirit/home/<USER>/operator/expect.hpp>
#include <boost/spirit/home/<USER>/operator/alternative.hpp>
#include <boost/spirit/home/<USER>/operator/sequential_or.hpp>
#include <boost/spirit/home/<USER>/operator/permutation.hpp>
#include <boost/spirit/home/<USER>/operator/difference.hpp>
#include <boost/spirit/home/<USER>/operator/list.hpp>
#include <boost/spirit/home/<USER>/operator/optional.hpp>
#include <boost/spirit/home/<USER>/operator/kleene.hpp>
#include <boost/spirit/home/<USER>/operator/plus.hpp>
#include <boost/spirit/home/<USER>/operator/and_predicate.hpp>
#include <boost/spirit/home/<USER>/operator/not_predicate.hpp>

#endif
