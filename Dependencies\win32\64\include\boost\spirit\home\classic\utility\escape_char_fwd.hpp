/*=============================================================================
    Copyright (c) 2006 <PERSON>
    http://spirit.sourceforge.net/

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#if !defined(BOOST_SPIRIT_ESCAPE_CHAR_FWD_HPP)
#define BOOST_SPIRIT_ESCAPE_CHAR_FWD_HPP

#include <boost/spirit/home/<USER>/namespace.hpp>

namespace boost { namespace spirit {

BOOST_SPIRIT_CLASSIC_NAMESPACE_BEGIN

    template <unsigned long Flags, typename CharT = char>
    struct escape_char_parser;

    template <
        class ParserT, typename ActionT,
        unsigned long Flags, typename CharT = char>
    struct escape_char_action;

BOOST_SPIRIT_CLASSIC_NAMESPACE_END

}} // namespace BOOST_SPIRIT_CLASSIC_NS

#endif

