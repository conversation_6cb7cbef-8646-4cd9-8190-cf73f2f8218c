//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON> Kaiser
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_LEX_STATIC_LEXER_VERSION_SEP_10_2009_0811PM)
#define BOOST_SPIRIT_LEX_STATIC_LEXER_VERSION_SEP_10_2009_0811PM

#if defined(_MSC_VER)
#pragma once
#endif

///////////////////////////////////////////////////////////////////////////////
// This is the version of the static lexer format. It is used to ensure a
// static lexer has been generated using the same data format as expected 
// by the executing application.
///////////////////////////////////////////////////////////////////////////////
#define SPIRIT_STATIC_LEXER_VERSION 0x010000

#endif
