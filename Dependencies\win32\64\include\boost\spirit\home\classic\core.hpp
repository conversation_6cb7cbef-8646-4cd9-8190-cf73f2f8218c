/*=============================================================================
    Copyright (c) 1998-2003 <PERSON>
    Copyright (c) 2001-2003 <PERSON>
    Copyright (c) 2001-2003 Hartmut <PERSON>
    Copyright (c) 2002-2003 <PERSON>
    Copyright (c) 2002 Raghavendra Satish
    Copyright (c) 2001 <PERSON>
    http://spirit.sourceforge.net/

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
=============================================================================*/
#if !defined(BOOST_SPIRIT_CORE_MAIN_HPP)
#define BOOST_SPIRIT_CORE_MAIN_HPP

#include <boost/spirit/home/<USER>/version.hpp>
#include <boost/spirit/home/<USER>/debug.hpp>

///////////////////////////////////////////////////////////////////////////////
//
//  Spirit.Core includes
//
///////////////////////////////////////////////////////////////////////////////

//  Spirit.Core.Kernel
#include <boost/spirit/home/<USER>/core/config.hpp>
#include <boost/spirit/home/<USER>/core/nil.hpp>
#include <boost/spirit/home/<USER>/core/match.hpp>
#include <boost/spirit/home/<USER>/core/parser.hpp>

//  Spirit.Core.Primitives
#include <boost/spirit/home/<USER>/core/primitives/primitives.hpp>
#include <boost/spirit/home/<USER>/core/primitives/numerics.hpp>

//  Spirit.Core.Scanner
#include <boost/spirit/home/<USER>/core/scanner/scanner.hpp>
#include <boost/spirit/home/<USER>/core/scanner/skipper.hpp>

//  Spirit.Core.NonTerminal
#include <boost/spirit/home/<USER>/core/non_terminal/subrule.hpp>
#include <boost/spirit/home/<USER>/core/non_terminal/rule.hpp>
#include <boost/spirit/home/<USER>/core/non_terminal/grammar.hpp>

//  Spirit.Core.Composite
#include <boost/spirit/home/<USER>/core/composite/actions.hpp>
#include <boost/spirit/home/<USER>/core/composite/composite.hpp>
#include <boost/spirit/home/<USER>/core/composite/directives.hpp>
#include <boost/spirit/home/<USER>/core/composite/epsilon.hpp>
#include <boost/spirit/home/<USER>/core/composite/sequence.hpp>
#include <boost/spirit/home/<USER>/core/composite/sequential_and.hpp>
#include <boost/spirit/home/<USER>/core/composite/sequential_or.hpp>
#include <boost/spirit/home/<USER>/core/composite/alternative.hpp>
#include <boost/spirit/home/<USER>/core/composite/difference.hpp>
#include <boost/spirit/home/<USER>/core/composite/intersection.hpp>
#include <boost/spirit/home/<USER>/core/composite/exclusive_or.hpp>
#include <boost/spirit/home/<USER>/core/composite/kleene_star.hpp>
#include <boost/spirit/home/<USER>/core/composite/positive.hpp>
#include <boost/spirit/home/<USER>/core/composite/optional.hpp>
#include <boost/spirit/home/<USER>/core/composite/list.hpp>
#include <boost/spirit/home/<USER>/core/composite/no_actions.hpp>

//  Deprecated interface includes
#include <boost/spirit/home/<USER>/actor/assign_actor.hpp>
#include <boost/spirit/home/<USER>/actor/push_back_actor.hpp>

#if defined(BOOST_SPIRIT_DEBUG)
    //////////////////////////////////
    #include <boost/spirit/home/<USER>/debug/parser_names.hpp>

#endif // BOOST_SPIRIT_DEBUG

#endif // BOOST_SPIRIT_CORE_MAIN_HPP

