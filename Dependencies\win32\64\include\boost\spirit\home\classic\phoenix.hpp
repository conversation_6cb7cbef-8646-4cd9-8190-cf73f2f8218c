/*=============================================================================
    Phoenix V1.2.1
    Copyright (c) 2001-2002 <PERSON>

  Distributed under the Boost Software License, Version 1.0. (See accompanying
  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/
#if !defined(BOOST_SPIRIT_PHOENIX_HPP)
#define BOOST_SPIRIT_PHOENIX_HPP

#include <boost/spirit/home/<USER>/phoenix/tuples.hpp>
#include <boost/spirit/home/<USER>/phoenix/tuple_helpers.hpp>
#include <boost/spirit/home/<USER>/phoenix/actor.hpp>
#include <boost/spirit/home/<USER>/phoenix/primitives.hpp>
#include <boost/spirit/home/<USER>/phoenix/composite.hpp>
#include <boost/spirit/home/<USER>/phoenix/functions.hpp>
#include <boost/spirit/home/<USER>/phoenix/operators.hpp>
#include <boost/spirit/home/<USER>/phoenix/special_ops.hpp>
#include <boost/spirit/home/<USER>/phoenix/statements.hpp>
#include <boost/spirit/home/<USER>/phoenix/binders.hpp>
#include <boost/spirit/home/<USER>/phoenix/closures.hpp>
#include <boost/spirit/home/<USER>/phoenix/casts.hpp>
#include <boost/spirit/home/<USER>/phoenix/new.hpp>

#endif // !defined(BOOST_SPIRIT_PHOENIX_HPP)
