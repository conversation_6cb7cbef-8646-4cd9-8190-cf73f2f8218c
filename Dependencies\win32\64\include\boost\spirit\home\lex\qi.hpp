//  Copyright (c) 2001-2011 <PERSON><PERSON><PERSON>
// 
//  Distributed under the Boost Software License, Version 1.0. (See accompanying 
//  file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

#if !defined(BOOST_SPIRIT_LEXER_QI_APR_21_2009_0205PM)
#define BOOST_SPIRIT_LEXER_QI_APR_21_2009_0205PM

#if defined(_MSC_VER)
#pragma once
#endif

#include <boost/spirit/home/<USER>/qi/state_switcher.hpp>
#include <boost/spirit/home/<USER>/qi/in_state.hpp>
#include <boost/spirit/home/<USER>/qi/plain_token.hpp>
#include <boost/spirit/home/<USER>/qi/plain_tokenid.hpp>
#include <boost/spirit/home/<USER>/qi/plain_tokenid_mask.hpp>
#include <boost/spirit/home/<USER>/qi/plain_raw_token.hpp>

#endif
